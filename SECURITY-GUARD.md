# Security Guard Implementation

## Overview

The Resume Tailor application now includes a comprehensive security guard system to protect against prompt injection attacks and system prompt exposure attempts. This guard acts as the first line of defense before any AI flows are executed.

## Architecture

### Input Guard Flow (`src/ai/flows/input-guard.ts`)

The security guard is implemented as a dedicated AI flow that:

1. **Analyzes user input** for potential security threats
2. **Detects prompt injection attempts** and system exposure requests
3. **Sanitizes safe content** while blocking malicious inputs
4. **Provides threat assessment** with detailed explanations

### Integration Points

The guard is integrated into all AI flows:

- `tailor-resume.ts` - Guards resume and job description inputs
- `simulate-ats-score.ts` - Guards resume and job description inputs  
- `refine-tailored-resume.ts` - Guards all inputs including feedback
- `justify-ats-score.ts` - Guards resume and job description inputs

## Security Features

### Threat Detection

The guard detects various types of malicious inputs:

1. **Prompt Injection Attempts**
   - Instructions to ignore previous instructions
   - Role-changing commands ("act as", "pretend to be")
   - Context manipulation attempts

2. **System Exposure Attempts**
   - Requests to reveal system prompts
   - Attempts to access internal instructions
   - Queries about model architecture or training

3. **Malicious Content**
   - Code injection attempts
   - Social engineering
   - Inappropriate content

### Threat Levels

- **HIGH**: Clear security threats - processing blocked
- **MEDIUM**: Suspicious patterns - content sanitized
- **LOW**: Minor irregularities - minimal sanitization
- **NONE**: Safe content - passes through unchanged

### Error Handling

When high-threat content is detected:

1. A `SecurityThreatError` is thrown
2. Processing is immediately halted
3. User receives a security alert message
4. No sensitive information is exposed

## Usage

### Automatic Protection

The guard runs automatically on all user inputs. No additional configuration is required.

### Error Messages

Users see friendly error messages without technical details:
- "Your input contains content that cannot be processed for security reasons"
- "Please review and modify your input"

### Logging

Security events are logged for monitoring:
- Threat level assessments
- Detected threat types
- Sanitization actions taken

## Testing

A test script is provided (`test-guard.js`) to verify the guard functionality:

```bash
node test-guard.js
```

The test covers:
- Normal resume content (should pass)
- Prompt injection attempts (should be blocked)
- System exposure attempts (should be blocked)

## Implementation Details

### Flow Structure

```typescript
export async function guardUserInput(input: InputGuardInput): Promise<InputGuardOutput> {
  const result = await inputGuardFlow(input);
  
  if (result.threatLevel === 'high' || !result.isSafe) {
    throw new SecurityThreatError(
      'Input contains potential security threats',
      result.threatLevel,
      result.detectedThreats
    );
  }
  
  return result;
}
```

### Integration Pattern

Each protected flow follows this pattern:

```typescript
export async function protectedFlow(input: FlowInput): Promise<FlowOutput> {
  await checkRateLimit();
  
  // Guard all user inputs
  const guardedInput1 = await guardUserInput({
    userInput: input.userText,
    inputType: 'resume' // or 'jobDescription' or 'feedback'
  });
  
  // Use sanitized inputs
  const sanitizedInput = {
    ...input,
    userText: guardedInput1.sanitizedInput
  };
  
  return actualFlow(sanitizedInput);
}
```

## Security Considerations

1. **Defense in Depth**: The guard is the first layer; additional security measures should be considered
2. **Regular Updates**: Security patterns should be updated as new attack vectors emerge
3. **Monitoring**: Security events should be monitored and analyzed
4. **False Positives**: Balance security with usability to minimize legitimate content being blocked

## Maintenance

### Adding New Threat Patterns

Update the guard prompt in `input-guard.ts` to include new threat detection patterns.

### Adjusting Sensitivity

Modify the threat level thresholds based on observed false positive/negative rates.

### Performance Monitoring

Monitor the guard's impact on response times and adjust as needed.

## Future Enhancements

1. **Machine Learning**: Implement ML-based threat detection
2. **Rate Limiting**: Add per-user security event rate limiting
3. **Reporting**: Create security dashboard for threat monitoring
4. **Customization**: Allow configuration of security sensitivity levels
