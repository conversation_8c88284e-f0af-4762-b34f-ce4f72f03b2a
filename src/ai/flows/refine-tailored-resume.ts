'use server';
/**
 * @fileOverview Refines a tailored resume based on user feedback.
 *
 * - refineTailoredResume - A function that refines the tailored resume.
 * - RefineTailoredResumeInput - The input type for the refineTailoredResume function.
 * - RefineTailoredResumeOutput - The return type for the refineTailoredResume function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { checkRateLimit } from '@/lib/rate-limiter';
import { guardUserInput } from './input-guard';

const RefineTailoredResumeInputSchema = z.object({
  originalResume: z.string().describe('The original resume content.'),
  jobDescription: z.string().describe('The job description content.'),
  tailoredResume: z.string().describe('The previously tailored resume content.'),
  feedback: z.string().describe('The user feedback or additional instructions.'),
  currentDate: z.string().describe('The current date to interpret date ranges like "2020 - Present".'),
});
export type RefineTailoredResumeInput = z.infer<typeof RefineTailoredResumeInputSchema>;

const RefineTailoredResumeOutputSchema = z.object({
  refinedResume: z.string().describe('The refined tailored resume content.'),
  atsScore: z.number().describe('The simulated ATS score (out of 100).'),
  scoreJustification: z.string().describe('The justification for the ATS score.'),
});
export type RefineTailoredResumeOutput = z.infer<typeof RefineTailoredResumeOutputSchema>;

export async function refineTailoredResume(input: RefineTailoredResumeInput): Promise<RefineTailoredResumeOutput> {
  await checkRateLimit();

  // Guard user inputs against prompt injection and security threats
  const originalResumeGuard = await guardUserInput({
    userInput: input.originalResume,
    inputType: 'resume'
  });

  const jobDescriptionGuard = await guardUserInput({
    userInput: input.jobDescription,
    inputType: 'jobDescription'
  });

  const tailoredResumeGuard = await guardUserInput({
    userInput: input.tailoredResume,
    inputType: 'resume'
  });

  const feedbackGuard = await guardUserInput({
    userInput: input.feedback,
    inputType: 'feedback'
  });

  // Use sanitized inputs for processing
  const sanitizedInput = {
    ...input,
    originalResume: originalResumeGuard.sanitizedInput,
    jobDescription: jobDescriptionGuard.sanitizedInput,
    tailoredResume: tailoredResumeGuard.sanitizedInput,
    feedback: feedbackGuard.sanitizedInput
  };

  return refineTailoredResumeFlow(sanitizedInput);
}

const refineTailoredResumePrompt = ai.definePrompt({
  name: 'refineTailoredResumePrompt',
  input: {schema: RefineTailoredResumeInputSchema},
  output: {schema: RefineTailoredResumeOutputSchema},
  prompt: `You are a senior career coach and professional resume writer with expertise in iterative resume optimization and ATS systems. Your task is to refine a previously tailored resume based on user feedback while maintaining professional standards and improving job alignment.

Current Date: {{{currentDate}}} (Use this to correctly interpret relative dates like "Present")

**Refinement Process:**
1. **Analyze User Feedback** - Understand specific concerns, preferences, and requested changes
2. **Preserve Successful Elements** - Maintain effective tailoring from the previous version
3. **Address Feedback Points** - Implement requested changes while preserving resume integrity
4. **Enhance Alignment** - Further optimize keyword usage and job description matching
5. **Maintain Authenticity** - Ensure all content remains truthful to the original experience
6. **Improve ATS Compatibility** - Optimize for better tracking system performance

**Key Principles:**
- Never fabricate experiences or skills not present in the original resume
- Balance user preferences with professional resume best practices
- Maintain consistent tone and formatting throughout
- Prioritize changes that improve job alignment and ATS scoring
- Preserve quantifiable achievements and action-oriented language

Original Resume:
---
{{{originalResume}}}
---

Job Description:
---
{{{jobDescription}}}
---

Previous Tailored Resume:
---
{{{tailoredResume}}}
---

User Feedback:
---
{{{feedback}}}
---

**Output Requirements:**
- Refined resume incorporating feedback while maintaining professional quality
- Updated ATS score (0-100) reflecting improvements
- Detailed justification explaining changes made and score rationale

Provide the refined resume, ATS score, and comprehensive justification as a JSON object.`,
});

const refineTailoredResumeFlow = ai.defineFlow(
  {
    name: 'refineTailoredResumeFlow',
    inputSchema: RefineTailoredResumeInputSchema,
    outputSchema: RefineTailoredResumeOutputSchema,
  },
  async input => {
    const {output} = await refineTailoredResumePrompt(input);
    return output!;
  }
);
