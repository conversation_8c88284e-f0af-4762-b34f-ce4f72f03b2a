// tailor-resume.ts
'use server';
/**
 * @fileOverview This file defines a Genkit flow for tailoring a resume to a specific job description.
 *
 * - tailorResume - A function that accepts a resume and job description, and returns a tailored resume, an ATS score, and a justification for the score.
 * - TailorResumeInput - The input type for the tailorResume function.
 * - TailorResumeOutput - The return type for the tailorResume function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { checkRateLimit } from '@/lib/rate-limiter';
import { guardUserInput } from './input-guard';

const TailorResumeInputSchema = z.object({
  resume: z.string().describe('The text of the original resume.'),
  jobDescription: z.string().describe('The text of the job description.'),
  currentDate: z.string().describe('The current date to interpret date ranges like "2020 - Present".'),
});
export type TailorResumeInput = z.infer<typeof TailorResumeInputSchema>;

const TailorResumeOutputSchema = z.object({
  tailoredResume: z.string().describe('The tailored resume text.'),
  atsScore: z.number().describe('A simulated ATS score (out of 100) for the tailored resume.'),
  scoreJustification: z.string().describe('A brief justification for the ATS score, highlighting key strengths and weaknesses.'),
});
export type TailorResumeOutput = z.infer<typeof TailorResumeOutputSchema>;

export async function tailorResume(input: TailorResumeInput): Promise<TailorResumeOutput> {
  await checkRateLimit();

  // Guard user inputs against prompt injection and security threats
  const resumeGuard = await guardUserInput({
    userInput: input.resume,
    inputType: 'resume'
  });

  const jobDescriptionGuard = await guardUserInput({
    userInput: input.jobDescription,
    inputType: 'jobDescription'
  });

  // Use sanitized inputs for processing
  const sanitizedInput = {
    ...input,
    resume: resumeGuard.sanitizedInput,
    jobDescription: jobDescriptionGuard.sanitizedInput
  };

  return tailorResumeFlow(sanitizedInput);
}

const tailorResumePrompt = ai.definePrompt({
  name: 'tailorResumePrompt',
  input: {schema: TailorResumeInputSchema},
  output: {schema: TailorResumeOutputSchema},
  prompt: `You are an expert career coach and professional resume writer with deep expertise in Applicant Tracking Systems (ATS). Your task is to help tailor a resume for a specific job description while providing accurate ATS scoring.

Current Date: {{{currentDate}}} (Use this to correctly interpret relative dates like "Present")

Analyze the provided job description and the user's current resume. Then, rewrite the resume to perfectly match the requirements and keywords in the job description.

Follow these instructions precisely:
1. Identify the most critical skills, experiences, and qualifications mentioned in the job description
2. Rewrite the resume's summary/objective section to be a powerful, concise statement that directly addresses the company and the specific role
3. Tailor the experience bullet points using action verbs and quantifiable achievements. Weave in keywords and phrases from the job description naturally
4. Re-order or emphasize skills in the "Skills" section to reflect the priorities of the job description
5. Ensure the tone is professional, confident, and achievement-oriented
6. Maintain a standard, clean resume format. Do not alter the core structure (e.g., Summary, Experience, Education, Skills sections) unless it significantly improves alignment

**CRITICAL HONESTY REQUIREMENTS:**
7. NEVER invent new experiences, skills, or technologies that are not already present in the user's resume
8. NEVER claim the user has used a skill/tool/technology in their work experience if it's not explicitly mentioned in their actual work history
9. NEVER link skills from the skills section to work experiences where they were not actually used
10. Only rephrase, reframe, and highlight the EXISTING content from the user's resume to better align with the job description
11. If a skill is listed in the skills section but not used in work experience, keep it in the skills section only - do not fabricate work experience with that skill
12. Maintain complete honesty and integrity - it's better to have a lower ATS score than to misrepresent the candidate's experience

**ATS SCORING AND JUSTIFICATION:**
13. For ATS scoring, evaluate keyword matching, skills alignment, experience relevance, and formatting compatibility
14. Provide actionable insights in the score justification, highlighting specific strengths and areas for improvement
15. In the justification, clearly distinguish between skills that are demonstrated through work experience vs. skills that are only listed

Here is the job description:
---
{{{jobDescription}}}
---

Here is the user's current resume:
---
{{{resume}}}
---

Output the tailored resume, ATS score (0-100), and detailed justification as a JSON object.`,
});

const tailorResumeFlow = ai.defineFlow(
  {
    name: 'tailorResumeFlow',
    inputSchema: TailorResumeInputSchema,
    outputSchema: TailorResumeOutputSchema,
  },
  async input => {
    const {output} = await tailorResumePrompt(input);
    return output!;
  }
);
